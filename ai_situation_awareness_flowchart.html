<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI态势感知流程图</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .flowchart {
            width: 100%;
            height: 800px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .legend {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .legend-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .legend-color {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 8px;
            vertical-align: middle;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI态势感知系统流程图</h1>
        
        <svg class="flowchart" viewBox="0 0 1200 700" xmlns="http://www.w3.org/2000/svg">
            <!-- 定义样式 -->
            <defs>
                <style>
                    .box { fill: #ffffff; stroke: #333; stroke-width: 2; }
                    .ai-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
                    .process-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
                    .data-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
                    .output-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
                    .text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
                    .title-text { font-size: 14px; font-weight: bold; }
                    .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
                    .red-arrow { stroke: #d32f2f; stroke-width: 2; fill: none; marker-end: url(#arrowhead-red); }
                    .dashed-line { stroke: #999; stroke-width: 1; stroke-dasharray: 5,5; fill: none; }
                </style>
                
                <!-- 箭头标记 -->
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                </marker>
                <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#d32f2f"/>
                </marker>
            </defs>
            
            <!-- 顶层：数据输入层 -->
            <circle cx="80" cy="80" r="35" class="ai-box"/>
            <text x="80" y="80" class="text title-text">AI数字人</text>
            
            <!-- 微步IT -->
            <rect x="200" y="50" width="80" height="40" rx="5" class="box"/>
            <text x="240" y="70" class="text">微步IT</text>
            
            <!-- API连接 -->
            <line x1="280" y1="70" x2="320" y2="70" class="arrow"/>
            <text x="300" y="65" class="text" style="font-size: 10px;">API</text>
            
            <!-- 情报智能体 -->
            <rect x="330" y="50" width="100" height="40" rx="5" class="process-box"/>
            <text x="380" y="70" class="text title-text">情报智能体</text>
            
            <!-- AI情报风险生成内容部署情报 -->
            <rect x="480" y="40" width="120" height="60" rx="5" class="output-box"/>
            <text x="540" y="60" class="text">AI情报风险生成内</text>
            <text x="540" y="80" class="text">容部署情报</text>
            
            <!-- 人员输入 -->
            <line x1="115" y1="80" x2="180" y2="80" class="arrow"/>
            <text x="147" y="75" class="text" style="font-size: 10px;">人员</text>
            
            <!-- 数据处理层 -->
            <rect x="250" y="130" width="60" height="30" rx="3" class="data-box"/>
            <text x="280" y="145" class="text">漏报</text>
            
            <rect x="350" y="130" width="80" height="30" rx="3" class="data-box"/>
            <text x="390" y="145" class="text">风险预警</text>
            <text x="390" y="155" class="text" style="font-size: 10px;">JSON</text>
            
            <rect x="470" y="130" width="80" height="30" rx="3" class="data-box"/>
            <text x="510" y="145" class="text">漏洞策略</text>
            <text x="510" y="155" class="text" style="font-size: 10px;">JSON</text>
            
            <!-- POC连接 -->
            <line x1="430" y1="130" x2="470" y2="130" class="red-arrow"/>
            <text x="450" y="125" class="text" style="font-size: 10px; fill: #d32f2f;">POC</text>
            
            <!-- 分隔线 -->
            <line x1="50" y1="200" x2="1150" y2="200" class="dashed-line"/>
            
            <!-- 左侧处理流程 -->
            <rect x="80" y="230" width="100" height="40" rx="5" class="process-box"/>
            <text x="130" y="250" class="text">规则验证智能体</text>
            
            <rect x="80" y="300" width="100" height="40" rx="5" class="box"/>
            <text x="130" y="320" class="text">模拟表包验证</text>
            
            <ellipse cx="130" cy="380" rx="40" ry="25" class="box"/>
            <text x="130" y="380" class="text">验证通过</text>
            
            <rect x="80" y="430" width="100" height="40" rx="5" class="output-box"/>
            <text x="130" y="450" class="text">规则智能体（新增）</text>
            
            <!-- 中间处理流程 -->
            <rect x="250" y="430" width="60" height="30" rx="3" class="box"/>
            <text x="280" y="445" class="text">规则</text>
            
            <rect x="250" y="490" width="60" height="40" rx="3" class="box"/>
            <text x="280" y="505" class="text">告警</text>
            <text x="280" y="520" class="text" style="font-size: 10px;">（含误报）</text>
            
            <circle cx="280" cy="580" r="25" class="ai-box"/>
            <text x="280" y="580" class="text">AI</text>
            
            <rect x="220" y="630" width="80" height="30" rx="3" class="output-box"/>
            <text x="260" y="645" class="text">精准告警</text>
            
            <!-- 右侧智能体处理 -->
            <rect x="450" y="230" width="100" height="40" rx="5" class="process-box"/>
            <text x="500" y="250" class="text">规则智能体</text>
            
            <!-- 资产处理流程 -->
            <rect x="380" y="320" width="60" height="30" rx="3" class="data-box"/>
            <text x="410" y="335" class="text">采源</text>
            
            <rect x="320" y="380" width="60" height="30" rx="3" class="box"/>
            <text x="350" y="395" class="text">被动</text>
            
            <rect x="420" y="380" width="60" height="30" rx="3" class="box"/>
            <text x="450" y="395" class="text">主动</text>
            
            <rect x="320" y="440" width="60" height="30" rx="3" class="box"/>
            <text x="350" y="455" class="text">观测</text>
            
            <rect x="420" y="440" width="80" height="30" rx="3" class="data-box"/>
            <text x="460" y="455" class="text">资产探测</text>
            
            <circle cx="350" cy="500" r="20" class="ai-box"/>
            <text x="350" y="500" class="text">AI</text>
            
            <rect x="320" y="540" width="80" height="30" rx="3" class="box"/>
            <text x="360" y="555" class="text">增强资产</text>
            
            <rect x="420" y="540" width="80" height="30" rx="3" class="data-box"/>
            <text x="460" y="555" class="text">已知资产</text>
            
            <!-- 右侧输出流程 -->
            <rect x="600" y="300" width="80" height="30" rx="3" class="output-box"/>
            <text x="640" y="315" class="text">漏洞策略无关验证</text>
            
            <rect x="720" y="250" width="60" height="30" rx="3" class="box"/>
            <text x="750" y="265" class="text">编测</text>
            
            <rect x="600" y="350" width="60" height="30" rx="3" class="box"/>
            <text x="630" y="365" class="text">留给A</text>
            
            <rect x="600" y="400" width="60" height="30" rx="3" class="box"/>
            <text x="630" y="415" class="text">编测</text>
            
            <rect x="600" y="450" width="80" height="30" rx="3" class="box"/>
            <text x="640" y="465" class="text">扫描任务</text>
            
            <rect x="600" y="500" width="80" height="40" rx="3" class="box"/>
            <text x="640" y="515" class="text">主动，周期</text>
            
            <rect x="600" y="560" width="80" height="30" rx="3" class="output-box"/>
            <text x="640" y="575" class="text">全网编测</text>
            
            <rect x="720" y="400" width="60" height="30" rx="3" class="box"/>
            <text x="750" y="415" class="text">漏洞</text>
            
            <rect x="720" y="480" width="80" height="30" rx="3" class="box"/>
            <text x="760" y="495" class="text">手动漏洞</text>
            
            <circle cx="760" cy="540" r="30" class="ai-box"/>
            <text x="760" y="535" class="text">AI辅助</text>
            <text x="760" y="550" class="text">审核</text>
            
            <!-- 底部流程 -->
            <rect x="450" y="600" width="200" height="40" rx="5" class="output-box"/>
            <text x="550" y="615" class="text">全网资产</text>
            <text x="550" y="630" class="text">已知资产/漏洞/未知人员资产</text>
            
            <!-- 最终输出 -->
            <rect x="800" y="600" width="60" height="30" rx="3" class="box"/>
            <text x="830" y="615" class="text">AI开关</text>
            
            <rect x="900" y="580" width="60" height="30" rx="3" class="ai-box"/>
            <text x="930" y="595" class="text">AI验证</text>
            
            <rect x="900" y="630" width="60" height="30" rx="3" class="box"/>
            <text x="930" y="645" class="text">人工验证</text>
            
            <rect x="1000" y="600" width="80" height="30" rx="3" class="output-box"/>
            <text x="1040" y="615" class="text">报告智能体</text>
            
            <!-- 连接线 -->
            <!-- 从情报智能体到数据处理 -->
            <line x1="380" y1="90" x2="280" y2="130" class="arrow"/>
            <line x1="380" y1="90" x2="390" y2="130" class="arrow"/>
            <line x1="380" y1="90" x2="510" y2="130" class="arrow"/>
            
            <!-- 左侧流程连接 -->
            <line x1="130" y1="270" x2="130" y2="300" class="arrow"/>
            <line x1="130" y1="340" x2="130" y2="355" class="arrow"/>
            <line x1="130" y1="405" x2="130" y2="430" class="arrow"/>
            
            <!-- 规则处理连接 -->
            <line x1="180" y1="450" x2="250" y2="445" class="arrow"/>
            <line x1="280" y1="460" x2="280" y2="490" class="arrow"/>
            <line x1="280" y1="530" x2="280" y2="555" class="arrow"/>
            <line x1="280" y1="605" x2="280" y2="630" class="arrow"/>
            
            <!-- 右侧流程连接 -->
            <line x1="500" y1="270" x2="410" y2="320" class="arrow"/>
            <line x1="410" y1="350" x2="350" y2="380" class="arrow"/>
            <line x1="410" y1="350" x2="450" y2="380" class="arrow"/>
            <line x1="350" y1="410" x2="350" y2="440" class="arrow"/>
            <line x1="450" y1="410" x2="460" y2="440" class="arrow"/>
            <line x1="350" y1="470" x2="350" y2="480" class="arrow"/>
            <line x1="350" y1="520" x2="360" y2="540" class="arrow"/>
            <line x1="460" y1="470" x2="460" y2="540" class="arrow"/>
            
            <!-- 输出流程连接 -->
            <line x1="680" y1="315" x2="720" y2="265" class="arrow"/>
            <line x1="640" y1="330" x2="640" y2="350" class="arrow"/>
            <line x1="630" y1="380" x2="630" y2="400" class="arrow"/>
            <line x1="640" y1="430" x2="640" y2="450" class="arrow"/>
            <line x1="640" y1="480" x2="640" y2="500" class="arrow"/>
            <line x1="640" y1="540" x2="640" y2="560" class="arrow"/>
            
            <line x1="680" y1="415" x2="720" y2="415" class="arrow"/>
            <line x1="750" y1="430" x2="750" y2="480" class="arrow"/>
            <line x1="760" y1="510" x2="760" y2="510" class="arrow"/>
            
            <!-- 最终连接 -->
            <line x1="650" y1="620" x2="800" y2="615" class="arrow"/>
            <line x1="860" y1="615" x2="900" y2="595" class="arrow"/>
            <line x1="860" y1="615" x2="900" y2="645" class="arrow"/>
            <line x1="960" y1="615" x2="1000" y2="615" class="arrow"/>
            
            <!-- 风险预测连接 -->
            <line x1="280" y1="160" x2="130" y2="230" class="red-arrow"/>
            <text x="200" y="190" class="text" style="font-size: 10px; fill: #d32f2f;">风险预测</text>
            
            <line x1="500" y1="160" x2="500" y2="230" class="arrow"/>
            
            <!-- 流量监测连接 -->
            <line x1="640" y1="300" x2="640" y2="280" class="red-arrow"/>
            <text x="680" y="290" class="text" style="font-size: 10px; fill: #d32f2f;">流量监测</text>
            
            <!-- 扫描连接 -->
            <line x1="500" y1="270" x2="600" y2="315" class="red-arrow"/>
            <text x="550" y="290" class="text" style="font-size: 10px; fill: #d32f2f;">扫描</text>
            
            <!-- 封堵连接 -->
            <line x1="680" y1="575" x2="730" y2="540" class="red-arrow"/>
            <text x="700" y="560" class="text" style="font-size: 10px; fill: #d32f2f;">封堵</text>
        </svg>
        
        <div class="legend">
            <h3>图例说明</h3>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #e3f2fd; border: 1px solid #1976d2;"></span>
                <span>AI智能体</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #f3e5f5; border: 1px solid #7b1fa2;"></span>
                <span>处理模块</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #e8f5e8; border: 1px solid #388e3c;"></span>
                <span>数据源</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #fff3e0; border: 1px solid #f57c00;"></span>
                <span>输出结果</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #ffffff; border: 1px solid #333;"></span>
                <span>常规组件</span>
            </div>
        </div>
    </div>
</body>
</html>
